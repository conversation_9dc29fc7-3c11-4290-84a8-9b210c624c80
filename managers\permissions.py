"""
Permissions module for different operator roles.
Defines what actions and sections each role can access.
"""

from typing import Dict, List, Any, Optional
from os import getenv



permissions = {
    "manager": {
        "service_open": True,
        "collection": True,
        "product_insert": True,
        "manager_mode": True,
        "delivery": True,
    },
    "service": {
        "service_open": True,
        "collection": True,
        "product_insert": True,
        "manager_mode": True,
        "delivery": True,
    },
    "hygiene": {
        "service_open": True,
        "collection": False,
        "product_insert": False,
        "manager_mode": False,
        "delivery": False,
    },
    "courier": {
        "service_open": False,
        "collection": True,
        "product_insert": True,
        "manager_mode": True,
        "delivery": True,
    },
    "montage": {
        "service_open": False,
        "collection": True,
        "product_insert": True,
        "manager_mode": True,
        "delivery": True,
    }
}


def get_hygiene_sections() -> List[int]:
    """
    Returns a list of sections available for hygiene.
    Reads comma separated string from HYGIENE_PERMITED_SECTIONS env variable.
    """
    hygiene_sections_str = getenv("HYGIENE_PERMITED_SECTIONS", "")
    
    if not hygiene_sections_str:
        return []
    
    return [int(section.strip()) for section in hygiene_sections_str.split(",")]


def get_permissions_by_role(role: str) -> Optional[Dict[str, Any]]:
    """
    Get permissions for a specific role.
    
    Args:
        role: The operator role (manager, service, hygiene, courier, montage)
        
    Returns:
        Dict containing role permissions or None if role not found
    """
    role_functions = {
        # "manager": get_manager_permissions,
        # "service": get_service_permissions,
        # "hygiene": get_hygiene_permissions,
        # "courier": get_courier_permissions,
        # "montage": get_montage_permissions
    }
    
    if role in role_functions:
        return role_functions[role]()
    
    return None
