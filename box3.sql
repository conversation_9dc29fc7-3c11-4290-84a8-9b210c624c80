-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 31, 2025 at 09:10 AM
-- Server version: 11.8.2-MariaDB-log
-- PHP Version: 8.3.18

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `box3`
--

-- --------------------------------------------------------

--
-- Table structure for table `abort_transactions`
--

CREATE TABLE `abort_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `box_sections`
--

CREATE TABLE `box_sections` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `box_uuid` varchar(255) NOT NULL,
  `section_id` int(11) NOT NULL,
  `identification_name` varchar(255) NOT NULL,
  `tempered` int(11) NOT NULL DEFAULT 0,
  `visible` int(11) NOT NULL DEFAULT 1,
  `blocked` int(11) NOT NULL DEFAULT 0,
  `service` int(11) NOT NULL DEFAULT 0,
  `title` varchar(255) DEFAULT NULL,
  `lock_id` varchar(50) NOT NULL,
  `led_section` int(11) NOT NULL,
  `fixed_pin` tinyint(1) NOT NULL DEFAULT 0,
  `pin` varchar(255) DEFAULT NULL,
  `size_category` int(11) NOT NULL,
  `mode` varchar(255) NOT NULL DEFAULT 'parcel',
  `type` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `box_sections`
--

INSERT INTO `box_sections` (`id`, `uuid`, `box_uuid`, `section_id`, `identification_name`, `tempered`, `visible`, `blocked`, `service`, `title`, `lock_id`, `led_section`, `fixed_pin`, `pin`, `size_category`, `mode`, `type`) VALUES
(1, '3603a9bd-f310-4d51-b39a-e4123d0b2b28', 'c580f199-9150-4662-a132-f12e5d7c625e', 1, '1', 1, 1, 0, 0, NULL, '1', 1, 0, NULL, 0, 'sale', ''),
(2, '033f62cd-aed5-4cf6-bc4d-5ff94fa26277', 'c580f199-9150-4662-a132-f12e5d7c625e', 2, '2', 1, 1, 0, 0, NULL, '2', 2, 0, NULL, 0, 'sale', ''),
(3, '87bee335-ca9d-4600-ade7-7ecb9b1af320', 'c580f199-9150-4662-a132-f12e5d7c625e', 3, '3', 1, 1, 0, 0, NULL, '3', 3, 0, NULL, 0, 'sale', ''),
(4, 'b967d6e0-b1c1-4fff-8f10-e5424d6408d6', 'c580f199-9150-4662-a132-f12e5d7c625e', 4, '4', 1, 1, 0, 0, NULL, '4', 4, 0, NULL, 0, 'sale', ''),
(5, 'dd494dcc-552c-49ed-9c90-6190d88a65c3', 'c580f199-9150-4662-a132-f12e5d7c625e', 5, '5', 1, 1, 0, 0, NULL, '5', 5, 0, NULL, 0, 'sale', ''),
(6, '59c19c16-5997-4fa4-b128-a6e574fbef1b', 'c580f199-9150-4662-a132-f12e5d7c625e', 6, '6', 1, 1, 0, 0, NULL, '6', 6, 0, NULL, 0, 'sale', ''),
(7, 'b9a19a0c-83d5-420e-82a0-df8c47ea0b7a', 'c580f199-9150-4662-a132-f12e5d7c625e', 7, '7', 1, 1, 0, 0, NULL, '7', 7, 0, NULL, 0, 'sale', ''),
(8, 'ce9541d4-95da-42f5-b776-d1376fcc50fb', 'c580f199-9150-4662-a132-f12e5d7c625e', 8, '8', 1, 1, 0, 0, NULL, '8', 8, 0, NULL, 0, 'sale', ''),
(9, '5a5aca50-d9fe-4373-bae2-8f8a0dc686fc', 'c580f199-9150-4662-a132-f12e5d7c625e', 9, '9', 1, 1, 0, 0, NULL, '9', 9, 0, NULL, 0, 'sale', ''),
(10, 'ff3eb5ef-f0d1-4802-98da-f86e08300f7e', 'c580f199-9150-4662-a132-f12e5d7c625e', 10, '10', 1, 1, 0, 0, NULL, '10', 10, 0, NULL, 0, 'sale', ''),
(11, 'd23cc6e6-d0ea-479b-985f-2084071a34cf', 'c580f199-9150-4662-a132-f12e5d7c625e', 11, '11', 1, 1, 0, 0, NULL, '11', 11, 0, NULL, 0, 'sale', ''),
(12, 'd1c4409a-a5fd-4e28-a40c-5aeb1723c57d', 'c580f199-9150-4662-a132-f12e5d7c625e', 12, '12', 1, 1, 0, 0, NULL, '12', 12, 0, NULL, 0, 'sale', ''),
(13, '0bc9baf9-bba5-4ad8-ac72-83e49516aa4b', 'c580f199-9150-4662-a132-f12e5d7c625e', 13, '13', 1, 1, 0, 0, NULL, '13', 13, 0, NULL, 0, 'sale', ''),
(14, 'd9ebfaf2-0eab-4af2-9fc2-6142bcd32367', 'c580f199-9150-4662-a132-f12e5d7c625e', 14, '14', 1, 1, 0, 0, NULL, '14', 14, 0, NULL, 0, 'sale', '');

-- --------------------------------------------------------

--
-- Table structure for table `box_settings`
--

CREATE TABLE `box_settings` (
  `id` int(11) NOT NULL,
  `box_uuid` uuid NOT NULL,
  `box_layout_id` int(11) NOT NULL,
  `box_layout` text NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- Dumping data for table `box_settings`
--

INSERT INTO `box_settings` (`id`, `box_uuid`, `box_layout_id`, `box_layout`, `changed_at`, `created_at`) VALUES
(1, 'c580f199-9150-4662-a132-f12e5d7c625e', 3, '{\n                \"columns\": [\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"1\",\n                            \"width_percent\": 100,\n                            \"tempered\": 0,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"2\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"3\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"4\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"5\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"6\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"32\",\n                            \"width_percent\": 100,\n                            \"service\": 1,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"7\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"8\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"9\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"10\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"11\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"12\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"13\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"14\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    }\n                ]\n                }', '2025-07-26 16:28:39', '2025-07-26 16:28:39');

-- --------------------------------------------------------

--
-- Table structure for table `box_status`
--

CREATE TABLE `box_status` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `box_uuid` varchar(255) DEFAULT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `ambient_temperature` varchar(255) DEFAULT NULL,
  `box_temperature` varchar(255) DEFAULT NULL,
  `humidity` varchar(255) DEFAULT NULL,
  `compressor_run` varchar(255) DEFAULT NULL,
  `heating_run` varchar(255) DEFAULT NULL,
  `lighting_on` varchar(255) DEFAULT NULL,
  `power_supply` varchar(255) DEFAULT NULL,
  `box_open` varchar(255) DEFAULT NULL,
  `mac` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `camera_records`
--

CREATE TABLE `camera_records` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `box_uuid` char(36) NOT NULL,
  `start` varchar(255) NOT NULL,
  `end` varchar(255) NOT NULL,
  `download_link` varchar(255) NOT NULL,
  `thumbnail_link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `close_totals_transactions`
--

CREATE TABLE `close_totals_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `commands`
--

CREATE TABLE `commands` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `params` text NOT NULL,
  `description` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `command_queues`
--

CREATE TABLE `command_queues` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `action` varchar(255) NOT NULL,
  `section` varchar(255) DEFAULT NULL,
  `execute_after` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `get_request_log`
--

CREATE TABLE `get_request_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `payload` varchar(255) DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `mac` varchar(255) NOT NULL,
  `reservation_number` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `contact_type` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `content` text DEFAULT NULL,
  `contact` varchar(255) NOT NULL,
  `section_id` varchar(50) DEFAULT NULL,
  `section_title` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `entered_pin` varchar(50) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `oauth_access_tokens`
--

CREATE TABLE `oauth_access_tokens` (
  `id` varchar(100) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `client_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `scopes` text DEFAULT NULL,
  `revoked` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `put_reservations`
--

CREATE TABLE `put_reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `reservation_uuid` varchar(255) DEFAULT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reload`
--

CREATE TABLE `reload` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `command` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `request_log`
--

CREATE TABLE `request_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `payload` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reservations`
--

CREATE TABLE `reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `customer_uuid` varchar(255) DEFAULT NULL,
  `box_uuid` varchar(255) DEFAULT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `reservation_number` varchar(255) DEFAULT NULL,
  `reservation_pin` varchar(255) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `age_control_required` tinyint(4) NOT NULL DEFAULT 0,
  `age_controlled` tinyint(4) NOT NULL DEFAULT 0,
  `price` double NOT NULL DEFAULT 0,
  `paid_status` varchar(255) DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp(),
  `pin_attempt` int(11) NOT NULL DEFAULT 0,
  `ean` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `product_status` varchar(255) DEFAULT NULL,
  `cover_image` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reservations_translations`
--

CREATE TABLE `reservations_translations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `reservation_id` bigint(20) UNSIGNED NOT NULL,
  `locale` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reservation_content`
--

CREATE TABLE `reservation_content` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `reservation_uuid` varchar(255) DEFAULT NULL,
  `section_uuid` varchar(255) DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'inserted',
  `inserted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sale_reservations`
--

CREATE TABLE `sale_reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` char(36) NOT NULL DEFAULT uuid(),
  `box_uuid` varchar(255) DEFAULT NULL,
  `section_id` varchar(255) DEFAULT NULL,
  `ean` varchar(255) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT 1,
  `reserved` int(11) DEFAULT 0,
  `reservation_pin` varchar(255) DEFAULT NULL,
  `age_control_required` tinyint(4) DEFAULT 0,
  `age_controlled` tinyint(4) NOT NULL DEFAULT 0,
  `price` double NOT NULL DEFAULT 0,
  `max_days` int(11) DEFAULT NULL,
  `paid_status` varchar(255) DEFAULT NULL,
  `type` varchar(256) NOT NULL DEFAULT 'ean',
  `cover_image` varchar(255) DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sale_reservations`
--

INSERT INTO `sale_reservations` (`id`, `uuid`, `box_uuid`, `section_id`, `ean`, `status`, `name`, `description`, `quantity`, `reserved`, `reservation_pin`, `age_control_required`, `age_controlled`, `price`, `max_days`, `paid_status`, `type`, `cover_image`, `last_update`, `created_at`) VALUES
(1, 'af46bd99-793c-4037-aa15-89d49c3ebc4e', NULL, '2', NULL, 0, NULL, NULL, 1, 1, '207626', 0, 0, 50, NULL, NULL, 'custom', NULL, '2025-07-10 13:47:19', '2025-07-10 13:08:49'),
(2, 'ae854947-b9bc-48d5-b981-b2c2f03cf367', NULL, '2', NULL, 0, NULL, NULL, 1, 0, NULL, 0, 0, 50, NULL, NULL, 'custom', NULL, '2025-07-10 13:49:16', '2025-07-10 13:49:16'),
(3, '7e4ba91c-44c9-49fc-bd0c-ed4858883207', NULL, '6', '2556562', 0, 'Biosil +', 'Biotin + Zinek + B6 Na vlasy, nehty, kuzi', 1, 0, NULL, 0, 0, 82, NULL, NULL, 'ean', 'https://4a8fa4e1-8948-4b4c-9baa-4839d158ad96.eu.jetveo.io/document/05126ad9-2d39-4293-91c6-90fd895b5cf1/ama_dalbam_1.jpg', '2025-07-22 09:21:41', '2025-07-22 09:21:41'),
(4, '762dca39-ba0d-4f9a-a073-f9a146f0669d', NULL, '4', '1337420', 0, '', 'Na redeni krve', 1, 0, NULL, 1, 0, 349, NULL, NULL, 'ean', 'https://3id-dev.eu.jetveo.io/document/3d1d5ab1-4d9b-4eab-baa4-ea0d1b6c0046/kyoto_night_1.jpg', '2025-07-27 15:21:28', '2025-07-27 15:21:28'),
(5, 'b84b48ff-a9e1-4ff5-b3d3-40686804d46e', NULL, '1', NULL, 1, NULL, NULL, 1, 0, NULL, 0, 0, 230, NULL, NULL, 'custom', NULL, '2025-07-29 14:06:38', '2025-07-29 14:06:38'),
(6, '56ada229-9a43-4cbf-a12d-a8d14c78885a', NULL, '4', NULL, 1, NULL, NULL, 1, 0, NULL, 0, 0, 123, NULL, NULL, 'custom', NULL, '2025-07-29 14:08:25', '2025-07-29 14:08:25');

-- --------------------------------------------------------

--
-- Table structure for table `sale_transactions`
--

CREATE TABLE `sale_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `result` varchar(255) DEFAULT NULL,
  `request` text DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `server_requests_queue`
--

CREATE TABLE `server_requests_queue` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `status` smallint(5) UNSIGNED NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `endpoint` varchar(255) NOT NULL,
  `action` varchar(255) NOT NULL,
  `error` varchar(255) DEFAULT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`payload`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `storage_transactions`
--

CREATE TABLE `storage_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `result` varchar(255) DEFAULT NULL,
  `request` text DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `set_request_log`
--

CREATE TABLE `set_request_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `payload` varchar(255) DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `storage_categories`
--

CREATE TABLE `storage_categories` (
  `id` int(11) NOT NULL,
  `size_category` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `storage_reservations`
--

CREATE TABLE `storage_reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` char(36) NOT NULL DEFAULT uuid(),
  `box_uuid` varchar(255) DEFAULT NULL,
  `section_id` varchar(255) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `reservation_pin` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `price` double NOT NULL DEFAULT 0,
  `max_days` int(11) DEFAULT NULL,
  `paid_status` varchar(255) DEFAULT NULL,
  `category` int(11) DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `timeline_log`
--

CREATE TABLE `timeline_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `mac` varchar(255) DEFAULT NULL,
  `serial_number` varchar(255) DEFAULT NULL,
  `reservation_number` varchar(255) DEFAULT NULL,
  `event_type` varchar(50) DEFAULT NULL,
  `entered_pin` varchar(50) DEFAULT NULL,
  `server_reservation_status` varchar(50) DEFAULT NULL,
  `server_reservation_uuid` varchar(255) DEFAULT NULL,
  `pin_type` varchar(50) DEFAULT NULL,
  `event_result` varchar(255) DEFAULT NULL,
  `status` smallint(6) DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`content`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `operator_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `section_identification` varchar(255) DEFAULT NULL,
  `section_id` varchar(255) DEFAULT NULL,
  `lock_id` varchar(50) DEFAULT NULL,
  `box_status` varchar(255) DEFAULT NULL,
  `user_type` varchar(255) DEFAULT NULL,
  `section_title` varchar(255) DEFAULT NULL,
  `message` varchar(255) DEFAULT NULL,
  `mode` varchar(255) DEFAULT NULL,
  `sequence_id` varchar(50) DEFAULT NULL,
  `session_id` varchar(50) DEFAULT NULL,
  `tempered_unlock` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `transaction`
--

CREATE TABLE `transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `payload` text DEFAULT NULL,
  `auth_code` varchar(255) DEFAULT NULL,
  `content` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `abort_transactions`
--
ALTER TABLE `abort_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `abort_transactions_uuid_unique` (`uuid`),
  ADD KEY `abort_transactions_uuid_index` (`uuid`);

--
-- Indexes for table `box_sections`
--
ALTER TABLE `box_sections`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `box_sections_uuid_unique` (`uuid`),
  ADD KEY `box_sections_uuid_index` (`uuid`),
  ADD KEY `box_sections_box_uuid_index` (`box_uuid`);

--
-- Indexes for table `box_settings`
--
ALTER TABLE `box_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `box_uuid` (`id`);

--
-- Indexes for table `box_status`
--
ALTER TABLE `box_status`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `box_status_uuid_unique` (`uuid`),
  ADD KEY `box_status_uuid_index` (`uuid`),
  ADD KEY `box_status_box_uuid_index` (`box_uuid`),
  ADD KEY `box_status_mac_index` (`mac`);

--
-- Indexes for table `camera_records`
--
ALTER TABLE `camera_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `camera_records_box_uuid_foreign` (`box_uuid`);

--
-- Indexes for table `close_totals_transactions`
--
ALTER TABLE `close_totals_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `close_totals_transactions_uuid_unique` (`uuid`),
  ADD KEY `close_totals_transactions_uuid_index` (`uuid`);

--
-- Indexes for table `commands`
--
ALTER TABLE `commands`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `command_queues`
--
ALTER TABLE `command_queues`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `command_queues_uuid_unique` (`uuid`),
  ADD KEY `command_queues_uuid_index` (`uuid`),
  ADD KEY `command_queues_action_index` (`action`),
  ADD KEY `command_queues_section_index` (`section`),
  ADD KEY `command_queues_execute_after_index` (`execute_after`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `get_request_log`
--
ALTER TABLE `get_request_log`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `get_request_log_uuid_unique` (`uuid`),
  ADD KEY `get_request_log_uuid_index` (`uuid`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_mac_idx` (`mac`),
  ADD KEY `notifications_reservation_number_idx` (`reservation_number`);

--
-- Indexes for table `oauth_access_tokens`
--
ALTER TABLE `oauth_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `oauth_access_tokens_user_id_index` (`user_id`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD KEY `password_resets_email_index` (`email`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `put_reservations`
--
ALTER TABLE `put_reservations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `put_reservations_uuid_unique` (`uuid`),
  ADD KEY `put_reservations_uuid_index` (`uuid`),
  ADD KEY `put_reservations_reservation_uuid_index` (`reservation_uuid`);

--
-- Indexes for table `reload`
--
ALTER TABLE `reload`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reload_uuid_unique` (`uuid`),
  ADD KEY `reload_uuid_index` (`uuid`),
  ADD KEY `reload_type_index` (`type`),
  ADD KEY `reload_command_index` (`command`);

--
-- Indexes for table `request_log`
--
ALTER TABLE `request_log`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `request_log_uuid_unique` (`uuid`),
  ADD KEY `request_log_uuid_index` (`uuid`),
  ADD KEY `request_log_date_time_index` (`date_time`);

--
-- Indexes for table `reservations`
--
ALTER TABLE `reservations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reservations_uuid_unique` (`uuid`),
  ADD KEY `reservations_uuid_index` (`uuid`);

--
-- Indexes for table `reservations_translations`
--
ALTER TABLE `reservations_translations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `reservations_translations_reservation_id_foreign` (`reservation_id`);

--
-- Indexes for table `reservation_content`
--
ALTER TABLE `reservation_content`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reservation_content_uuid_unique` (`uuid`),
  ADD KEY `reservation_content_uuid_index` (`uuid`),
  ADD KEY `reservation_content_reservation_uuid_index` (`reservation_uuid`),
  ADD KEY `reservation_content_section_uuid_index` (`section_uuid`);

--
-- Indexes for table `sale_reservations`
--
ALTER TABLE `sale_reservations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uuid_unique` (`uuid`);

--
-- Indexes for table `sale_transactions`
--
ALTER TABLE `sale_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `sale_transactions_uuid_unique` (`uuid`),
  ADD KEY `sale_transactions_uuid_index` (`uuid`);

--
-- Indexes for table `server_requests_queue`
--
ALTER TABLE `server_requests_queue`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `set_request_log`
--
ALTER TABLE `set_request_log`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `set_request_log_uuid_unique` (`uuid`),
  ADD KEY `set_request_log_uuid_index` (`uuid`);

--
-- Indexes for table `storage_categories`
--
ALTER TABLE `storage_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id` (`id`),
  ADD KEY `id_2` (`id`);

--
-- Indexes for table `storage_transactions`
--
ALTER TABLE `storage_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `storage_transactions_uuid_unique` (`uuid`),
  ADD KEY `storage_transactions_uuid_index` (`uuid`);

--
-- Indexes for table `storage_reservations`
--
ALTER TABLE `storage_reservations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uuid_unique` (`uuid`);

--
-- Indexes for table `timeline_log`
--
ALTER TABLE `timeline_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `timeline_mac_idx` (`mac`),
  ADD KEY `timeline_event_type_idx` (`event_type`),
  ADD KEY `timeline_event_result_idx` (`event_result`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `transactions_uuid_unique` (`uuid`),
  ADD KEY `transactions_uuid_index` (`uuid`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `abort_transactions`
--
ALTER TABLE `abort_transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `box_sections`
--
ALTER TABLE `box_sections`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=81;

--
-- AUTO_INCREMENT for table `box_status`
--
ALTER TABLE `box_status`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `camera_records`
--
ALTER TABLE `camera_records`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `close_totals_transactions`
--
ALTER TABLE `close_totals_transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `commands`
--
ALTER TABLE `commands`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62;

--
-- AUTO_INCREMENT for table `command_queues`
--
ALTER TABLE `command_queues`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `get_request_log`
--
ALTER TABLE `get_request_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=78230;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=73;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `put_reservations`
--
ALTER TABLE `put_reservations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `reload`
--
ALTER TABLE `reload`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `request_log`
--
ALTER TABLE `request_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `reservations`
--
ALTER TABLE `reservations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `reservations_translations`
--
ALTER TABLE `reservations_translations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `reservation_content`
--
ALTER TABLE `reservation_content`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `sale_reservations`
--
ALTER TABLE `sale_reservations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `sale_transactions`
--
ALTER TABLE `sale_transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `server_requests_queue`
--
ALTER TABLE `server_requests_queue`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `set_request_log`
--
ALTER TABLE `set_request_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `storage_reservations`
--
ALTER TABLE `storage_reservations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `storage_transactions`
--
ALTER TABLE `storage_transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `timeline_log`
--
ALTER TABLE `timeline_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2916;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
